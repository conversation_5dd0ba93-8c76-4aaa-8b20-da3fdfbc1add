import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/quest_model.dart';
import '../models/achievement_model.dart';
import '../models/wallet_model.dart';
import '../models/notification_model.dart';
import 'auth_service.dart';

class ApiService {
  late final Dio _dio;
  final AuthService _authService;

  ApiService(this._authService) {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.apiBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final token = await _authService.getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            await _authService.logout();
          }
          handler.next(error);
        },
      ),
    );

    // Add logging interceptor in debug mode
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => debugPrint(obj.toString()),
    ));
  }

  // User API endpoints
  Future<User> getCurrentUser() async {
    final response = await _dio.get('/users/me');
    return User.fromJson(response.data['data']);
  }

  Future<User> updateUser(Map<String, dynamic> userData) async {
    final response = await _dio.put('/users/me', data: userData);
    return User.fromJson(response.data['data']);
  }

  // Quest API endpoints
  Future<List<Quest>> getQuests({
    QuestCategory? category,
    QuestDifficulty? difficulty,
    QuestStatus? status,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    if (category != null) queryParams['category'] = category.name;
    if (difficulty != null) queryParams['difficulty'] = difficulty.name;
    if (status != null) queryParams['status'] = status.name;

    final response = await _dio.get('/quests', queryParameters: queryParams);
    final List<dynamic> questsData = response.data['data'];
    return questsData.map((json) => Quest.fromJson(json)).toList();
  }

  Future<Quest> getQuest(String questId) async {
    final response = await _dio.get('/quests/$questId');
    return Quest.fromJson(response.data['data']);
  }

  Future<Quest> joinQuest(String questId) async {
    final response = await _dio.post('/quests/$questId/join');
    return Quest.fromJson(response.data['data']);
  }

  Future<Quest> completeQuestStep(String questId, String stepId) async {
    final response = await _dio.post('/quests/$questId/steps/$stepId/complete');
    return Quest.fromJson(response.data['data']);
  }

  // Achievement API endpoints
  Future<List<Achievement>> getAchievements({
    AchievementCategory? category,
    AchievementRarity? rarity,
    bool? isSecret,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    if (category != null) queryParams['category'] = category.name;
    if (rarity != null) queryParams['rarity'] = rarity.name;
    if (isSecret != null) queryParams['is_secret'] = isSecret;

    final response = await _dio.get('/achievements', queryParameters: queryParams);
    final List<dynamic> achievementsData = response.data['data'];
    return achievementsData.map((json) => Achievement.fromJson(json)).toList();
  }

  Future<List<UserAchievement>> getUserAchievements() async {
    final response = await _dio.get('/users/me/achievements');
    final List<dynamic> achievementsData = response.data['data'];
    return achievementsData.map((json) => UserAchievement.fromJson(json)).toList();
  }

  // Wallet API endpoints
  Future<Wallet> getWallet() async {
    final response = await _dio.get('/wallet');
    return Wallet.fromJson(response.data['data']);
  }

  Future<List<WalletTransaction>> getWalletTransactions({
    WalletTransactionType? type,
    WalletTransactionStatus? status,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    if (type != null) queryParams['type'] = type.name;
    if (status != null) queryParams['status'] = status.name;

    final response = await _dio.get('/wallet/transactions', queryParameters: queryParams);
    final List<dynamic> transactionsData = response.data['data'];
    return transactionsData.map((json) => WalletTransaction.fromJson(json)).toList();
  }

  // Notification API endpoints
  Future<List<NotificationModel>> getNotifications({
    NotificationType? type,
    bool? isRead,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    if (type != null) queryParams['type'] = type.name;
    if (isRead != null) queryParams['is_read'] = isRead;

    final response = await _dio.get('/notifications', queryParameters: queryParams);
    final List<dynamic> notificationsData = response.data['data'];
    return notificationsData.map((json) => NotificationModel.fromJson(json)).toList();
  }

  Future<NotificationModel> markNotificationAsRead(String notificationId) async {
    final response = await _dio.put('/notifications/$notificationId/read');
    return NotificationModel.fromJson(response.data['data']);
  }

  Future<void> markAllNotificationsAsRead() async {
    await _dio.put('/notifications/read-all');
  }

  // Leaderboard API endpoints
  Future<List<User>> getLeaderboard({
    String period = 'all-time',
    int page = 1,
    int limit = 50,
  }) async {
    final queryParams = <String, dynamic>{
      'period': period,
      'page': page,
      'limit': limit,
    };

    final response = await _dio.get('/leaderboard', queryParameters: queryParams);
    final List<dynamic> usersData = response.data['data'];
    return usersData.map((json) => User.fromJson(json)).toList();
  }

  Future<Map<String, dynamic>> getUserRank() async {
    final response = await _dio.get('/leaderboard/me');
    return response.data['data'];
  }

  // Search endpoints
  Future<Map<String, dynamic>> search(String query, {
    List<String>? types,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, dynamic>{
      'q': query,
      'page': page,
      'limit': limit,
    };

    if (types != null && types.isNotEmpty) {
      queryParams['types'] = types.join(',');
    }

    final response = await _dio.get('/search', queryParameters: queryParams);
    return response.data['data'];
  }
}

final apiServiceProvider = Provider<ApiService>((ref) {
  final authService = ref.watch(authServiceProvider);
  return ApiService(authService);
});
