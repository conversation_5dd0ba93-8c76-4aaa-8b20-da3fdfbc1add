import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildAccountSection(context),
          const SizedBox(height: 24),
          _buildAppearanceSection(context),
          const SizedBox(height: 24),
          _buildNotificationSection(context),
          const SizedBox(height: 24),
          _buildPrivacySection(context),
          const SizedBox(height: 24),
          _buildSupportSection(context),
          const SizedBox(height: 24),
          _buildAboutSection(context),
        ],
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Account',
      children: [
        _buildSettingsTile(
          context,
          icon: Icons.person,
          title: 'Edit Profile',
          subtitle: 'Update your personal information',
          onTap: () => _navigateToEditProfile(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.email,
          title: 'Email Settings',
          subtitle: '<EMAIL>',
          onTap: () => _showEmailSettings(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.lock,
          title: 'Change Password',
          subtitle: 'Update your password',
          onTap: () => _showChangePassword(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.link,
          title: 'Connected Accounts',
          subtitle: 'Manage social connections',
          onTap: () => _showConnectedAccounts(context),
        ),
      ],
    );
  }

  Widget _buildAppearanceSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Appearance',
      children: [
        _buildSettingsTile(
          context,
          icon: Icons.palette,
          title: 'Theme',
          subtitle: 'System default',
          trailing: DropdownButton<String>(
            value: 'System',
            items: ['Light', 'Dark', 'System'].map((theme) {
              return DropdownMenuItem(
                value: theme,
                child: Text(theme),
              );
            }).toList(),
            onChanged: (value) => _changeTheme(context, value),
            underline: Container(),
          ),
          onTap: null,
        ),
        _buildSettingsTile(
          context,
          icon: Icons.language,
          title: 'Language',
          subtitle: 'English (US)',
          onTap: () => _showLanguageSettings(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.text_fields,
          title: 'Font Size',
          subtitle: 'Medium',
          onTap: () => _showFontSizeSettings(context),
        ),
      ],
    );
  }

  Widget _buildNotificationSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Notifications',
      children: [
        _buildSwitchTile(
          context,
          icon: Icons.notifications,
          title: 'Push Notifications',
          subtitle: 'Receive quest updates and achievements',
          value: true,
          onChanged: (value) => _toggleNotifications(context, value),
        ),
        _buildSwitchTile(
          context,
          icon: Icons.email,
          title: 'Email Notifications',
          subtitle: 'Get weekly summaries and important updates',
          value: false,
          onChanged: (value) => _toggleEmailNotifications(context, value),
        ),
        _buildSwitchTile(
          context,
          icon: Icons.vibration,
          title: 'Vibrations',
          subtitle: 'Vibrate for notifications',
          value: true,
          onChanged: (value) => _toggleVibrations(context, value),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.schedule,
          title: 'Quiet Hours',
          subtitle: '10:00 PM - 8:00 AM',
          onTap: () => _showQuietHours(context),
        ),
      ],
    );
  }

  Widget _buildPrivacySection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Privacy & Security',
      children: [
        _buildSwitchTile(
          context,
          icon: Icons.visibility,
          title: 'Profile Visibility',
          subtitle: 'Allow others to see your profile',
          value: true,
          onChanged: (value) => _toggleProfileVisibility(context, value),
        ),
        _buildSwitchTile(
          context,
          icon: Icons.analytics,
          title: 'Analytics',
          subtitle: 'Help improve the app with usage data',
          value: true,
          onChanged: (value) => _toggleAnalytics(context, value),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.shield,
          title: 'Privacy Policy',
          subtitle: 'Read our privacy policy',
          onTap: () => _showPrivacyPolicy(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.description,
          title: 'Terms of Service',
          subtitle: 'Read our terms of service',
          onTap: () => _showTermsOfService(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.download,
          title: 'Download My Data',
          subtitle: 'Export your account data',
          onTap: () => _downloadData(context),
        ),
      ],
    );
  }

  Widget _buildSupportSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Support',
      children: [
        _buildSettingsTile(
          context,
          icon: Icons.help,
          title: 'Help Center',
          subtitle: 'Get help and find answers',
          onTap: () => _openHelpCenter(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.bug_report,
          title: 'Report a Bug',
          subtitle: 'Let us know about issues',
          onTap: () => _reportBug(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.feedback,
          title: 'Send Feedback',
          subtitle: 'Share your thoughts and suggestions',
          onTap: () => _sendFeedback(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.star,
          title: 'Rate the App',
          subtitle: 'Rate us on the app store',
          onTap: () => _rateApp(context),
        ),
      ],
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'About',
      children: [
        _buildSettingsTile(
          context,
          icon: Icons.info,
          title: 'App Version',
          subtitle: '1.0.0 (Build 1)',
          onTap: null,
        ),
        _buildSettingsTile(
          context,
          icon: Icons.update,
          title: 'Check for Updates',
          subtitle: 'Manually check for app updates',
          onTap: () => _checkForUpdates(context),
        ),
        _buildSettingsTile(
          context,
          icon: Icons.code,
          title: 'Open Source Licenses',
          subtitle: 'View third-party licenses',
          onTap: () => _showLicenses(context),
        ),
        const SizedBox(height: 32),
        _buildDangerZone(context),
      ],
    );
  }

  Widget _buildDangerZone(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Danger Zone',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildDangerTile(
            context,
            icon: Icons.logout,
            title: 'Sign Out',
            subtitle: 'Sign out of your account',
            onTap: () => _signOut(context),
          ),
          _buildDangerTile(
            context,
            icon: Icons.delete_forever,
            title: 'Delete Account',
            subtitle: 'Permanently delete your account and data',
            onTap: () => _deleteAccount(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, {required String title, required List<Widget> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: trailing ?? (onTap != null ? const Icon(Icons.chevron_right) : null),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      secondary: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
    );
  }

  Widget _buildDangerTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.red),
      title: Text(title, style: const TextStyle(color: Colors.red)),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right, color: Colors.red),
      onTap: onTap,
    );
  }

  // Action handlers
  void _navigateToEditProfile(BuildContext context) {
    Navigator.of(context).pushNamed('/profile');
  }

  void _showEmailSettings(BuildContext context) {
    // Show email settings dialog
  }

  void _showChangePassword(BuildContext context) {
    // Show change password dialog
  }

  void _showConnectedAccounts(BuildContext context) {
    // Show connected accounts page
  }

  void _changeTheme(BuildContext context, String? theme) {
    // Handle theme change
  }

  void _showLanguageSettings(BuildContext context) {
    // Show language selection dialog
  }

  void _showFontSizeSettings(BuildContext context) {
    // Show font size selection dialog
  }

  void _toggleNotifications(BuildContext context, bool value) {
    // Handle notification toggle
  }

  void _toggleEmailNotifications(BuildContext context, bool value) {
    // Handle email notification toggle
  }

  void _toggleVibrations(BuildContext context, bool value) {
    // Handle vibration toggle
  }

  void _showQuietHours(BuildContext context) {
    // Show quiet hours time picker
  }

  void _toggleProfileVisibility(BuildContext context, bool value) {
    // Handle profile visibility toggle
  }

  void _toggleAnalytics(BuildContext context, bool value) {
    // Handle analytics toggle
  }

  void _showPrivacyPolicy(BuildContext context) {
    // Show privacy policy
  }

  void _showTermsOfService(BuildContext context) {
    // Show terms of service
  }

  void _downloadData(BuildContext context) {
    // Trigger data download
  }

  void _openHelpCenter(BuildContext context) {
    // Open help center
  }

  void _reportBug(BuildContext context) {
    // Show bug report form
  }

  void _sendFeedback(BuildContext context) {
    // Show feedback form
  }

  void _rateApp(BuildContext context) {
    // Open app store rating
  }

  void _checkForUpdates(BuildContext context) {
    // Check for app updates
  }

  void _showLicenses(BuildContext context) {
    showLicensePage(context: context);
  }

  void _signOut(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Handle sign out
              Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  void _deleteAccount(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'This action cannot be undone. All your data, including quests, achievements, and wallet balance will be permanently deleted.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _confirmDeleteAccount(context),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteAccount(BuildContext context) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Final Confirmation'),
        content: const TextField(
          decoration: InputDecoration(
            labelText: 'Type "DELETE" to confirm',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Handle account deletion
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Account deletion initiated')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
