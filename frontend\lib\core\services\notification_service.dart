import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'websocket_service.dart';
import 'storage_service.dart';

// Notification types
enum NotificationType {
  info,
  success,
  warning,
  error,
  achievement,
  questComplete,
  questUpdate,
  leaderboardUpdate,
  social,
}

// Notification model
class AppNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final bool isRead;
  final IconData? icon;
  final Color? color;

  const AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.data,
    this.isRead = false,
    this.icon,
    this.color,
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    Map<String, dynamic>? data,
    bool? isRead,
    IconData? icon,
    Color? color,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      icon: icon ?? this.icon,
      color: color ?? this.color,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.name,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'data': data,
      'isRead': isRead,
    };
  }

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationType.info,
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
      data: json['data'],
      isRead: json['isRead'] ?? false,
    );
  }

  IconData get defaultIcon {
    switch (type) {
      case NotificationType.achievement:
        return Icons.emoji_events;
      case NotificationType.questComplete:
        return Icons.task_alt;
      case NotificationType.questUpdate:
        return Icons.update;
      case NotificationType.leaderboardUpdate:
        return Icons.leaderboard;
      case NotificationType.social:
        return Icons.people;
      case NotificationType.success:
        return Icons.check_circle;
      case NotificationType.warning:
        return Icons.warning;
      case NotificationType.error:
        return Icons.error;
      default:
        return Icons.info;
    }
  }

  Color get defaultColor {
    switch (type) {
      case NotificationType.achievement:
        return Colors.amber;
      case NotificationType.questComplete:
        return Colors.green;
      case NotificationType.questUpdate:
        return Colors.blue;
      case NotificationType.leaderboardUpdate:
        return Colors.purple;
      case NotificationType.social:
        return Colors.teal;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}

// Notification service
class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();

  NotificationService._();

  final List<AppNotification> _notifications = [];
  final StreamController<List<AppNotification>> _notificationsController =
      StreamController<List<AppNotification>>.broadcast();

  late StreamSubscription _wsNotificationSubscription;
  late StreamSubscription _wsAchievementSubscription;
  late StreamSubscription _wsQuestSubscription;
  late StreamSubscription _wsLeaderboardSubscription;

  // Stream for notifications
  Stream<List<AppNotification>> get notificationsStream => _notificationsController.stream;

  // Getters
  List<AppNotification> get notifications => List.unmodifiable(_notifications);
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// Initialize the notification service
  Future<void> initialize() async {
    await _loadStoredNotifications();
    _setupWebSocketListeners();
  }

  /// Setup WebSocket listeners
  void _setupWebSocketListeners() {
    final wsService = WebSocketService.instance;

    // Listen to WebSocket notifications
    _wsNotificationSubscription = wsService.notificationStream.listen(_handleWebSocketNotification);
    _wsAchievementSubscription = wsService.achievementStream.listen(_handleAchievementNotification);
    _wsQuestSubscription = wsService.questUpdateStream.listen(_handleQuestNotification);
    _wsLeaderboardSubscription = wsService.leaderboardStream.listen(_handleLeaderboardNotification);
  }

  /// Handle WebSocket notification
  void _handleWebSocketNotification(Map<String, dynamic> data) {
    final notification = AppNotification(
      id: data['id'] ?? _generateId(),
      title: data['title'] ?? 'Notification',
      message: data['message'] ?? '',
      type: _parseNotificationType(data['notification_type']),
      timestamp: DateTime.now(),
      data: data,
    );

    addNotification(notification);
  }

  /// Handle achievement notification
  void _handleAchievementNotification(Map<String, dynamic> data) {
    final notification = AppNotification(
      id: _generateId(),
      title: 'Achievement Unlocked!',
      message: data['achievement_name'] ?? 'You\'ve unlocked a new achievement!',
      type: NotificationType.achievement,
      timestamp: DateTime.now(),
      data: data,
    );

    addNotification(notification);
  }

  /// Handle quest notification
  void _handleQuestNotification(Map<String, dynamic> data) {
    final String status = data['status'] ?? '';
    final String questTitle = data['quest_title'] ?? 'Quest';

    NotificationType type;
    String title;
    String message;

    switch (status) {
      case 'completed':
        type = NotificationType.questComplete;
        title = 'Quest Completed!';
        message = 'You\'ve completed "$questTitle"';
        break;
      case 'updated':
        type = NotificationType.questUpdate;
        title = 'Quest Updated';
        message = 'Progress updated for "$questTitle"';
        break;
      default:
        type = NotificationType.questUpdate;
        title = 'Quest Update';
        message = 'Quest "$questTitle" has been updated';
    }

    final notification = AppNotification(
      id: _generateId(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );

    addNotification(notification);
  }

  /// Handle leaderboard notification
  void _handleLeaderboardNotification(Map<String, dynamic> data) {
    final notification = AppNotification(
      id: _generateId(),
      title: 'Leaderboard Update',
      message: data['message'] ?? 'The leaderboard has been updated!',
      type: NotificationType.leaderboardUpdate,
      timestamp: DateTime.now(),
      data: data,
    );

    addNotification(notification);
  }

  /// Add a new notification
  void addNotification(AppNotification notification) {
    _notifications.insert(0, notification); // Add to beginning for newest first
    _notificationsController.add(_notifications);
    _saveNotifications();
  }

  /// Create a local notification
  void showLocalNotification({
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    Map<String, dynamic>? data,
  }) {
    final notification = AppNotification(
      id: _generateId(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );

    addNotification(notification);
  }

  /// Mark notification as read
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _notificationsController.add(_notifications);
      _saveNotifications();
    }
  }

  /// Mark all notifications as read
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    _notificationsController.add(_notifications);
    _saveNotifications();
  }

  /// Remove notification
  void removeNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    _notificationsController.add(_notifications);
    _saveNotifications();
  }

  /// Clear all notifications
  void clearAll() {
    _notifications.clear();
    _notificationsController.add(_notifications);
    _saveNotifications();
  }

  /// Load stored notifications
  Future<void> _loadStoredNotifications() async {
    try {
      final storedData = StorageService.getAppData('notifications');
      if (storedData != null && storedData is List) {
        _notifications.clear();
        for (final item in storedData) {
          if (item is Map<String, dynamic>) {
            _notifications.add(AppNotification.fromJson(item));
          }
        }
        _notificationsController.add(_notifications);
      }
    } catch (e) {
      print('Error loading notifications: $e');
    }
  }

  /// Save notifications to storage
  Future<void> _saveNotifications() async {
    try {
      final data = _notifications.take(100).map((n) => n.toJson()).toList(); // Keep only last 100
      await StorageService.saveAppData('notifications', data);
    } catch (e) {
      print('Error saving notifications: $e');
    }
  }

  /// Parse notification type from string
  NotificationType _parseNotificationType(String? typeString) {
    if (typeString == null) return NotificationType.info;
    return NotificationType.values.firstWhere(
      (e) => e.name == typeString,
      orElse: () => NotificationType.info,
    );
  }

  /// Generate unique ID
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Dispose of resources
  void dispose() {
    _wsNotificationSubscription.cancel();
    _wsAchievementSubscription.cancel();
    _wsQuestSubscription.cancel();
    _wsLeaderboardSubscription.cancel();
    _notificationsController.close();
  }
}

// Riverpod providers
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService.instance;
});

final notificationsProvider = StreamProvider<List<AppNotification>>((ref) {
  final service = ref.watch(notificationServiceProvider);
  return service.notificationsStream;
});

final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notifications = ref.watch(notificationsProvider);
  return notifications.when(
    data: (notifications) => notifications.where((n) => !n.isRead).length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});
