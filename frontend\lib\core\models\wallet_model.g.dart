// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Wallet _$WalletFromJson(Map<String, dynamic> json) => Wallet(
      id: json['id'] as String,
      userId: json['userId'] as String,
      balance: (json['balance'] as num).toInt(),
      transactions: (json['transactions'] as List<dynamic>)
          .map((e) => WalletTransaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$WalletToJson(Wallet instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'balance': instance.balance,
      'transactions': instance.transactions,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

WalletTransaction _$WalletTransactionFromJson(Map<String, dynamic> json) =>
    WalletTransaction(
      id: json['id'] as String,
      walletId: json['walletId'] as String,
      type: $enumDecode(_$WalletTransactionTypeEnumMap, json['type']),
      amount: (json['amount'] as num).toInt(),
      description: json['description'] as String,
      referenceId: json['referenceId'] as String?,
      status: $enumDecode(_$WalletTransactionStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$WalletTransactionToJson(WalletTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'walletId': instance.walletId,
      'type': _$WalletTransactionTypeEnumMap[instance.type]!,
      'amount': instance.amount,
      'description': instance.description,
      'referenceId': instance.referenceId,
      'status': _$WalletTransactionStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$WalletTransactionTypeEnumMap = {
  WalletTransactionType.credit: 'credit',
  WalletTransactionType.debit: 'debit',
  WalletTransactionType.reward: 'reward',
  WalletTransactionType.purchase: 'purchase',
  WalletTransactionType.transfer: 'transfer',
  WalletTransactionType.refund: 'refund',
};

const _$WalletTransactionStatusEnumMap = {
  WalletTransactionStatus.pending: 'pending',
  WalletTransactionStatus.completed: 'completed',
  WalletTransactionStatus.failed: 'failed',
  WalletTransactionStatus.cancelled: 'cancelled',
};
