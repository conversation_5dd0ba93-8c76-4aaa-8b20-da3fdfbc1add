#!/usr/bin/env python3
"""
<PERSON>ript to fix deprecated withOpacity calls in Flutter project.
Replaces .withOpacity(value) with .withValues(alpha: value)
"""

import os
import re
import sys

def fix_withopacity_in_file(file_path):
    """Fix withOpacity calls in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to match .withOpacity(number)
        pattern = r'\.withOpacity\(([0-9]*\.?[0-9]+)\)'
        replacement = r'.withValues(alpha: \1)'
        
        # Count matches before replacement
        matches = re.findall(pattern, content)
        if not matches:
            return 0
        
        # Replace all occurrences
        new_content = re.sub(pattern, replacement, content)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Fixed {len(matches)} withOpacity calls in {file_path}")
        return len(matches)
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return 0

def main():
    """Main function to process all Dart files in frontend/lib directory."""
    frontend_lib_path = "frontend/lib"
    
    if not os.path.exists(frontend_lib_path):
        print(f"Error: {frontend_lib_path} directory not found")
        sys.exit(1)
    
    total_fixes = 0
    files_processed = 0
    
    # Walk through all .dart files
    for root, dirs, files in os.walk(frontend_lib_path):
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                fixes = fix_withopacity_in_file(file_path)
                total_fixes += fixes
                if fixes > 0:
                    files_processed += 1
    
    print(f"\nSummary:")
    print(f"Files processed: {files_processed}")
    print(f"Total withOpacity calls fixed: {total_fixes}")

if __name__ == "__main__":
    main()
