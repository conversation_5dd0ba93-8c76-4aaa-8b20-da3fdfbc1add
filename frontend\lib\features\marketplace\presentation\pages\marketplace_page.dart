import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/marketplace_item_model.dart';
import '../../../../shared/widgets/custom_card.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../providers/marketplace_provider.dart';

class MarketplacePage extends ConsumerStatefulWidget {
  const MarketplacePage({super.key});

  @override
  ConsumerState<MarketplacePage> createState() => _MarketplacePageState();
}

class _MarketplacePageState extends ConsumerState<MarketplacePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load marketplace items when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(marketplaceNotifierProvider.notifier).loadItems();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final marketplaceState = ref.watch(marketplaceNotifierProvider);
    final filteredItems = ref.watch(filteredItemsProvider);
    final featuredItems = ref.watch(featuredItemsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Marketplace'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: theme.scaffoldBackgroundColor,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.shopping_bag_outlined),
            onPressed: () => _showPurchaseHistory(),
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: marketplaceState.isLoading && marketplaceState.items.isEmpty,
        child: Column(
          children: [
            // Search Bar
            Padding(
              padding: const EdgeInsets.all(16),              child: CustomTextField(
                controller: _searchController,
                hint: 'Search marketplace...',
                prefixIcon: Icons.search,
                onChanged: (value) {
                  ref.read(marketplaceNotifierProvider.notifier).setSearchQuery(value);
                },
              ),
            ),

            // Category Filter Chips
            SizedBox(
              height: 50,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: MarketplaceItemCategory.values.length + 1,
                itemBuilder: (context, index) {
                  if (index == 0) {
                    return _buildCategoryChip(null, 'All');
                  }
                  final category = MarketplaceItemCategory.values[index - 1];
                  return _buildCategoryChip(category, category.displayName);
                },
              ),
            ),

            const SizedBox(height: 16),

            // Error Message
            if (marketplaceState.error != null)
              Padding(
                padding: const EdgeInsets.all(16),
                child: CustomCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: theme.colorScheme.error,
                          size: 48,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Error loading marketplace',
                          style: theme.textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          marketplaceState.error!,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        CustomButton(
                          text: 'Retry',
                          onPressed: () {
                            ref.read(marketplaceNotifierProvider.notifier)
                                .loadItems(refresh: true);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Content Tabs
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // All Items
                  _buildItemGrid(filteredItems),
                  
                  // Featured Items
                  _buildFeaturedSection(featuredItems),
                  
                  // Categories
                  _buildCategoriesView(),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _tabController.index,
        onTap: (index) {
          _tabController.animateTo(index);
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.grid_view),
            label: 'All Items',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.star),
            label: 'Featured',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.category),
            label: 'Categories',
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(MarketplaceItemCategory? category, String label) {
    final theme = Theme.of(context);
    final isSelected = ref.watch(marketplaceNotifierProvider).selectedCategory == category;

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          ref.read(marketplaceNotifierProvider.notifier)
              .setSelectedCategory(selected ? category : null);
        },
        backgroundColor: theme.colorScheme.surface,
        selectedColor: theme.colorScheme.primary.withOpacity(0.2),
        labelStyle: TextStyle(
          color: isSelected ? theme.colorScheme.primary : null,
        ),
      ),
    );
  }

  Widget _buildItemGrid(List<MarketplaceItem> items) {
    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No items found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(marketplaceNotifierProvider.notifier)
            .loadItems(refresh: true);
      },
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) {
          return _buildItemCard(items[index]);
        },
      ),
    );
  }

  Widget _buildFeaturedSection(List<MarketplaceItem> featuredItems) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Featured Items',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Featured Items Carousel
          SizedBox(
            height: 220,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: featuredItems.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 180,
                  margin: const EdgeInsets.only(right: 12),
                  child: _buildItemCard(featuredItems[index]),
                );
              },
            ),
          ),
          
          const SizedBox(height: 24),
          
          // On Sale Section
          Text(
            'On Sale',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          Consumer(
            builder: (context, ref, child) {
              final onSaleItems = ref.watch(onSaleItemsProvider);
              if (onSaleItems.isEmpty) {
                return const Center(
                  child: Text('No items on sale'),
                );
              }
              
              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.8,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: onSaleItems.length,
                itemBuilder: (context, index) {
                  return _buildItemCard(onSaleItems[index]);
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: MarketplaceItemCategory.values.length,
      itemBuilder: (context, index) {
        final category = MarketplaceItemCategory.values[index];
        return CustomCard(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              child: Text(
                category.icon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
            title: Text(category.displayName),
            subtitle: Consumer(
              builder: (context, ref, child) {
                final items = ref.watch(marketplaceNotifierProvider).items;
                final categoryItems = items.where((item) => item.category == category).length;
                return Text('$categoryItems items');
              },
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              ref.read(marketplaceNotifierProvider.notifier).setSelectedCategory(category);
              _tabController.animateTo(0); // Switch to All Items tab
            },
          ),
        );
      },
    );
  }

  Widget _buildItemCard(MarketplaceItem item) {
    final theme = Theme.of(context);

    return CustomCard(
      child: InkWell(
        onTap: () => _showItemDetails(item),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Item Image
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: item.imageUrl != null
                  ? ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                      child: Image.network(
                        item.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(item),
                      ),
                    )
                  : _buildPlaceholderImage(item),
            ),
            
            // Item Info
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Text(
                      item.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const Spacer(),
                    
                    // Price and Buy Button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (item.isOnSale) ...[
                              Text(
                                '${item.originalPrice} ${item.currency}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  decoration: TextDecoration.lineThrough,
                                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                                ),
                              ),
                            ],
                            Row(
                              children: [
                                Text(
                                  '${item.price}',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: item.isOnSale ? AppTheme.errorColor : null,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  _getCurrencyIcon(item.currency),
                                  size: 16,
                                  color: AppTheme.warningColor,
                                ),
                              ],
                            ),
                          ],
                        ),
                        
                        if (item.isAvailable)
                          IconButton(
                            icon: const Icon(Icons.add_shopping_cart),
                            onPressed: () => _purchaseItem(item),
                            style: IconButton.styleFrom(
                              backgroundColor: theme.colorScheme.primary,
                              foregroundColor: theme.colorScheme.onPrimary,
                              padding: const EdgeInsets.all(8),
                            ),
                          )
                        else
                          Text(
                            'Sold Out',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.error,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage(MarketplaceItem item) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Center(
        child: Text(
          item.category.icon,
          style: const TextStyle(fontSize: 40),
        ),
      ),
    );
  }

  IconData _getCurrencyIcon(String currency) {
    switch (currency.toLowerCase()) {
      case 'points':
        return Icons.stars;
      case 'coins':
        return Icons.monetization_on;
      case 'premium':
        return Icons.diamond;
      default:
        return Icons.attach_money;
    }
  }

  void _showItemDetails(MarketplaceItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.4,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item Image
                      Container(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: item.imageUrl != null
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Image.network(
                                  item.imageUrl!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(item),
                                ),
                              )
                            : _buildPlaceholderImage(item),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Item Details
                      Text(
                        item.name,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      Text(
                        item.description,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Stats
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildStatItem('Category', item.category.displayName),
                          _buildStatItem('Rarity', item.rarity.displayName),
                          _buildStatItem('Stock', '${item.quantity}/${item.maxQuantity}'),
                        ],
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Purchase Section
                      CustomCard(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Price:',
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  Row(
                                    children: [
                                      if (item.isOnSale) ...[
                                        Text(
                                          '${item.originalPrice}',
                                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                            decoration: TextDecoration.lineThrough,
                                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                      ],
                                      Text(
                                        '${item.price}',
                                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: item.isOnSale ? AppTheme.errorColor : null,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Icon(
                                        _getCurrencyIcon(item.currency),
                                        color: AppTheme.warningColor,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: 16),
                              
                              if (item.isAvailable)
                                CustomButton(
                                  text: 'Purchase Now',
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _purchaseItem(item);
                                  },
                                  width: double.infinity,
                                )
                              else
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    item.status.displayName,
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      color: Theme.of(context).colorScheme.error,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  void _purchaseItem(MarketplaceItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Purchase ${item.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Are you sure you want to purchase this item?'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('${item.price}'),
                const SizedBox(width: 4),
                Icon(_getCurrencyIcon(item.currency), color: AppTheme.warningColor),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await ref.read(marketplaceNotifierProvider.notifier)
                  .purchaseItem(item.id, 1);
              
              if (success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Successfully purchased ${item.name}!'),
                    backgroundColor: AppTheme.successColor,
                  ),
                );
              } else if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Purchase failed. Please try again.'),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              }
            },
            child: const Text('Purchase'),
          ),
        ],
      ),
    );
  }

  void _showPurchaseHistory() {
    final purchases = ref.read(userPurchasesProvider);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.3,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Purchase History',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              
              // Content
              Expanded(
                child: purchases.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.shopping_bag_outlined,
                              size: 64,
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No purchases yet',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        controller: scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: purchases.length,
                        itemBuilder: (context, index) {
                          final purchase = purchases[index];
                          return CustomCard(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                child: Text(purchase.item.category.icon),
                              ),
                              title: Text(purchase.item.name),
                              subtitle: Text(
                                'Purchased ${_formatDate(purchase.purchasedAt)}',
                              ),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text('${purchase.totalPrice}'),
                                      const SizedBox(width: 4),
                                      Icon(
                                        _getCurrencyIcon(purchase.currency),
                                        size: 16,
                                        color: AppTheme.warningColor,
                                      ),
                                    ],
                                  ),
                                  Text(
                                    purchase.status.name.toUpperCase(),
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: _getPurchaseStatusColor(purchase.status),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getPurchaseStatusColor(MarketplacePurchaseStatus status) {
    switch (status) {
      case MarketplacePurchaseStatus.completed:
        return AppTheme.successColor;
      case MarketplacePurchaseStatus.pending:
        return AppTheme.warningColor;
      case MarketplacePurchaseStatus.failed:
      case MarketplacePurchaseStatus.refunded:
        return AppTheme.errorColor;
    }
  }
}
