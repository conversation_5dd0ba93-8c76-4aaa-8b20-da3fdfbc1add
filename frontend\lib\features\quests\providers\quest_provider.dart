import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/quest_model.dart';
import '../../../core/services/api_service.dart';

class QuestState {
  final bool isLoading;
  final List<Quest> quests;
  final Quest? selectedQuest;
  final String? error;
  final bool hasMore;
  final int currentPage;

  const QuestState({
    this.isLoading = false,
    this.quests = const [],
    this.selectedQuest,
    this.error,
    this.hasMore = true,
    this.currentPage = 1,
  });

  QuestState copyWith({
    bool? isLoading,
    List<Quest>? quests,
    Quest? selectedQuest,
    String? error,
    bool? hasMore,
    int? currentPage,
  }) {
    return QuestState(
      isLoading: isLoading ?? this.isLoading,
      quests: quests ?? this.quests,
      selectedQuest: selectedQuest ?? this.selectedQuest,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class QuestNotifier extends StateNotifier<QuestState> {
  final ApiService _apiService;

  QuestNotifier(this._apiService) : super(const QuestState());

  Future<void> loadQuests({
    QuestCategory? category,
    QuestDifficulty? difficulty,
    QuestStatus? status,
    bool refresh = false,
  }) async {
    if (refresh) {
      state = state.copyWith(
        isLoading: true,
        currentPage: 1,
        quests: [],
        hasMore: true,
        error: null,
      );
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final newQuests = await _apiService.getQuests(
        category: category,
        difficulty: difficulty,
        status: status,
        page: refresh ? 1 : state.currentPage,
        limit: 20,
      );

      final allQuests = refresh 
          ? newQuests 
          : [...state.quests, ...newQuests];

      state = state.copyWith(
        isLoading: false,
        quests: allQuests,
        hasMore: newQuests.length >= 20,
        currentPage: refresh ? 2 : state.currentPage + 1,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadQuestDetails(String questId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final quest = await _apiService.getQuest(questId);
      state = state.copyWith(
        isLoading: false,
        selectedQuest: quest,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> joinQuest(String questId) async {
    try {
      final updatedQuest = await _apiService.joinQuest(questId);
      
      // Update the quest in the list
      final updatedQuests = state.quests.map((quest) {
        return quest.id == questId ? updatedQuest : quest;
      }).toList();

      state = state.copyWith(
        quests: updatedQuests,
        selectedQuest: state.selectedQuest?.id == questId ? updatedQuest : state.selectedQuest,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> completeQuestStep(String questId, String stepId) async {
    try {
      final updatedQuest = await _apiService.completeQuestStep(questId, stepId);
      
      // Update the quest in the list
      final updatedQuests = state.quests.map((quest) {
        return quest.id == questId ? updatedQuest : quest;
      }).toList();

      state = state.copyWith(
        quests: updatedQuests,
        selectedQuest: state.selectedQuest?.id == questId ? updatedQuest : state.selectedQuest,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  void clearSelectedQuest() {
    state = state.copyWith(selectedQuest: null);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final questNotifierProvider = StateNotifierProvider<QuestNotifier, QuestState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return QuestNotifier(apiService);
});

// Filtered quest providers
final dailyQuestsProvider = Provider<List<Quest>>((ref) {
  final quests = ref.watch(questNotifierProvider).quests;
  return quests.where((quest) => quest.category == QuestCategory.daily).toList();
});

final weeklyQuestsProvider = Provider<List<Quest>>((ref) {
  final quests = ref.watch(questNotifierProvider).quests;
  return quests.where((quest) => quest.category == QuestCategory.weekly).toList();
});

final activeQuestsProvider = Provider<List<Quest>>((ref) {
  final quests = ref.watch(questNotifierProvider).quests;
  return quests.where((quest) => quest.status == QuestStatus.active).toList();
});

final completedQuestsProvider = Provider<List<Quest>>((ref) {
  final quests = ref.watch(questNotifierProvider).quests;
  return quests.where((quest) => quest.status == QuestStatus.completed).toList();
});

// Quest loading state providers
final questsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(questNotifierProvider).isLoading;
});

final questErrorProvider = Provider<String?>((ref) {
  return ref.watch(questNotifierProvider).error;
});

final selectedQuestProvider = Provider<Quest?>((ref) {
  return ref.watch(questNotifierProvider).selectedQuest;
});
