import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final String? actionUrl;
  final Map<String, dynamic>? metadata;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    required this.isRead,
    required this.createdAt,
    this.readAt,
    this.actionUrl,
    this.metadata,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) => _$NotificationModelFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);
}

@JsonEnum()
enum NotificationType {
  @JsonValue('achievement')
  achievement,
  @JsonValue('quest_completed')
  questCompleted,
  @JsonValue('quest_available')
  questAvailable,
  @JsonValue('friend_request')
  friendRequest,
  @JsonValue('leaderboard_update')
  leaderboardUpdate,
  @JsonValue('wallet_transaction')
  walletTransaction,
  @JsonValue('system')
  system,
  @JsonValue('marketplace')
  marketplace,
}

@JsonEnum()
enum NotificationPriority {
  @JsonValue('low')
  low,
  @JsonValue('normal')
  normal,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.achievement:
        return 'Achievement';
      case NotificationType.questCompleted:
        return 'Quest Completed';
      case NotificationType.questAvailable:
        return 'Quest Available';
      case NotificationType.friendRequest:
        return 'Friend Request';
      case NotificationType.leaderboardUpdate:
        return 'Leaderboard Update';
      case NotificationType.walletTransaction:
        return 'Wallet Transaction';
      case NotificationType.system:
        return 'System';
      case NotificationType.marketplace:
        return 'Marketplace';
    }
  }
}

extension NotificationPriorityExtension on NotificationPriority {
  String get displayName {
    switch (this) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.normal:
        return 'Normal';
      case NotificationPriority.high:
        return 'High';
      case NotificationPriority.urgent:
        return 'Urgent';
    }
  }
}
