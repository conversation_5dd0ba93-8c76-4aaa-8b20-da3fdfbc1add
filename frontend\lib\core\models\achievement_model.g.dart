// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'achievement_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      iconUrl: json['iconUrl'] as String?,
      category: $enumDecode(_$AchievementCategoryEnumMap, json['category']),
      rarity: $enumDecode(_$AchievementRarityEnumMap, json['rarity']),
      pointsReward: (json['pointsReward'] as num).toInt(),
      requirements: (json['requirements'] as List<dynamic>)
          .map(
              (e) => AchievementRequirement.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isSecret: json['isSecret'] as bool,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'iconUrl': instance.iconUrl,
      'category': _$AchievementCategoryEnumMap[instance.category]!,
      'rarity': _$AchievementRarityEnumMap[instance.rarity]!,
      'pointsReward': instance.pointsReward,
      'requirements': instance.requirements,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isSecret': instance.isSecret,
      'metadata': instance.metadata,
    };

const _$AchievementCategoryEnumMap = {
  AchievementCategory.quests: 'quests',
  AchievementCategory.social: 'social',
  AchievementCategory.exploration: 'exploration',
  AchievementCategory.collection: 'collection',
  AchievementCategory.competition: 'competition',
  AchievementCategory.milestone: 'milestone',
  AchievementCategory.special: 'special',
};

const _$AchievementRarityEnumMap = {
  AchievementRarity.common: 'common',
  AchievementRarity.uncommon: 'uncommon',
  AchievementRarity.rare: 'rare',
  AchievementRarity.epic: 'epic',
  AchievementRarity.legendary: 'legendary',
};

AchievementRequirement _$AchievementRequirementFromJson(
        Map<String, dynamic> json) =>
    AchievementRequirement(
      id: json['id'] as String,
      type: $enumDecode(_$AchievementRequirementTypeEnumMap, json['type']),
      target: json['target'] as String,
      requiredValue: (json['requiredValue'] as num).toInt(),
      currentValue: (json['currentValue'] as num).toInt(),
      isCompleted: json['isCompleted'] as bool,
    );

Map<String, dynamic> _$AchievementRequirementToJson(
        AchievementRequirement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$AchievementRequirementTypeEnumMap[instance.type]!,
      'target': instance.target,
      'requiredValue': instance.requiredValue,
      'currentValue': instance.currentValue,
      'isCompleted': instance.isCompleted,
    };

const _$AchievementRequirementTypeEnumMap = {
  AchievementRequirementType.questCompletion: 'quest_completion',
  AchievementRequirementType.pointsEarned: 'points_earned',
  AchievementRequirementType.levelReached: 'level_reached',
  AchievementRequirementType.daysActive: 'days_active',
  AchievementRequirementType.socialConnections: 'social_connections',
  AchievementRequirementType.marketplaceTransactions:
      'marketplace_transactions',
  AchievementRequirementType.itemsCollected: 'items_collected',
};

UserAchievement _$UserAchievementFromJson(Map<String, dynamic> json) =>
    UserAchievement(
      id: json['id'] as String,
      userId: json['userId'] as String,
      achievementId: json['achievementId'] as String,
      achievement:
          Achievement.fromJson(json['achievement'] as Map<String, dynamic>),
      unlockedAt: DateTime.parse(json['unlockedAt'] as String),
      isNotified: json['isNotified'] as bool,
    );

Map<String, dynamic> _$UserAchievementToJson(UserAchievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'achievementId': instance.achievementId,
      'achievement': instance.achievement,
      'unlockedAt': instance.unlockedAt.toIso8601String(),
      'isNotified': instance.isNotified,
    };
