import 'package:json_annotation/json_annotation.dart';

part 'quest_model.g.dart';

@JsonSerializable()
class Quest {
  final String id;
  final String title;
  final String description;
  final String? imageUrl;
  final QuestCategory category;
  final QuestDifficulty difficulty;
  final QuestStatus status;
  final int pointsReward;
  final int experienceReward;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? startDate;
  final DateTime? endDate;
  final int maxParticipants;
  final int currentParticipants;
  final List<QuestStep> steps;
  final List<String> prerequisites;
  final List<QuestReward> rewards;
  final bool isPublic;
  final Map<String, dynamic>? metadata;

  const Quest({
    required this.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.category,
    required this.difficulty,
    required this.status,
    required this.pointsReward,
    required this.experienceReward,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.startDate,
    this.endDate,
    required this.maxParticipants,
    required this.currentParticipants,
    required this.steps,
    required this.prerequisites,
    required this.rewards,
    required this.isPublic,
    this.metadata,
  });

  factory Quest.fromJson(Map<String, dynamic> json) => _$QuestFromJson(json);
  Map<String, dynamic> toJson() => _$QuestToJson(this);
}

@JsonSerializable()
class QuestStep {
  final String id;
  final String title;
  final String description;
  final int order;
  final QuestStepType type;
  final Map<String, dynamic>? requirements;
  final bool isCompleted;
  final DateTime? completedAt;

  const QuestStep({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    required this.type,
    this.requirements,
    required this.isCompleted,
    this.completedAt,
  });

  factory QuestStep.fromJson(Map<String, dynamic> json) => _$QuestStepFromJson(json);
  Map<String, dynamic> toJson() => _$QuestStepToJson(this);
}

@JsonSerializable()
class QuestReward {
  final String id;
  final QuestRewardType type;
  final String name;
  final String description;
  final int quantity;
  final String? imageUrl;
  final Map<String, dynamic>? metadata;

  const QuestReward({
    required this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.quantity,
    this.imageUrl,
    this.metadata,
  });

  factory QuestReward.fromJson(Map<String, dynamic> json) => _$QuestRewardFromJson(json);
  Map<String, dynamic> toJson() => _$QuestRewardToJson(this);
}

@JsonEnum()
enum QuestCategory {
  @JsonValue('daily')
  daily,
  @JsonValue('weekly')
  weekly,
  @JsonValue('monthly')
  monthly,
  @JsonValue('special')
  special,
  @JsonValue('community')
  community,
  @JsonValue('personal')
  personal,
  @JsonValue('education')
  education,
  @JsonValue('fitness')
  fitness,
  @JsonValue('creative')
  creative,
  @JsonValue('social')
  social,
}

@JsonEnum()
enum QuestDifficulty {
  @JsonValue('easy')
  easy,
  @JsonValue('medium')
  medium,
  @JsonValue('hard')
  hard,
  @JsonValue('expert')
  expert,
}

@JsonEnum()
enum QuestStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('published')
  published,
  @JsonValue('active')
  active,
  @JsonValue('completed')
  completed,
  @JsonValue('expired')
  expired,
  @JsonValue('cancelled')
  cancelled,
}

@JsonEnum()
enum QuestStepType {
  @JsonValue('action')
  action,
  @JsonValue('submission')
  submission,
  @JsonValue('verification')
  verification,
  @JsonValue('timer')
  timer,
  @JsonValue('location')
  location,
  @JsonValue('social')
  social,
}

@JsonEnum()
enum QuestRewardType {
  @JsonValue('points')
  points,
  @JsonValue('badge')
  badge,
  @JsonValue('item')
  item,
  @JsonValue('currency')
  currency,
  @JsonValue('experience')
  experience,
  @JsonValue('nft')
  nft,
}

extension QuestCategoryExtension on QuestCategory {
  String get displayName {
    switch (this) {
      case QuestCategory.daily:
        return 'Daily';
      case QuestCategory.weekly:
        return 'Weekly';
      case QuestCategory.monthly:
        return 'Monthly';
      case QuestCategory.special:
        return 'Special';
      case QuestCategory.community:
        return 'Community';
      case QuestCategory.personal:
        return 'Personal';
      case QuestCategory.education:
        return 'Education';
      case QuestCategory.fitness:
        return 'Fitness';
      case QuestCategory.creative:
        return 'Creative';
      case QuestCategory.social:
        return 'Social';
    }
  }
}

extension QuestDifficultyExtension on QuestDifficulty {
  String get displayName {
    switch (this) {
      case QuestDifficulty.easy:
        return 'Easy';
      case QuestDifficulty.medium:
        return 'Medium';
      case QuestDifficulty.hard:
        return 'Hard';
      case QuestDifficulty.expert:
        return 'Expert';
    }
  }

  int get multiplier {
    switch (this) {
      case QuestDifficulty.easy:
        return 1;
      case QuestDifficulty.medium:
        return 2;
      case QuestDifficulty.hard:
        return 3;
      case QuestDifficulty.expert:
        return 5;
    }
  }
}

extension QuestStatusExtension on QuestStatus {
  String get displayName {
    switch (this) {
      case QuestStatus.draft:
        return 'Draft';
      case QuestStatus.published:
        return 'Published';
      case QuestStatus.active:
        return 'Active';
      case QuestStatus.completed:
        return 'Completed';
      case QuestStatus.expired:
        return 'Expired';
      case QuestStatus.cancelled:
        return 'Cancelled';
    }
  }

  bool get isActive => this == QuestStatus.active;
  bool get isCompleted => this == QuestStatus.completed;
  bool get canJoin => this == QuestStatus.published || this == QuestStatus.active;
}
