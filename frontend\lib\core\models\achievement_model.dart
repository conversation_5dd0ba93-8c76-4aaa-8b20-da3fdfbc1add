import 'package:json_annotation/json_annotation.dart';

part 'achievement_model.g.dart';

@JsonSerializable()
class Achievement {
  final String id;
  final String name;
  final String description;
  final String? iconUrl;
  final AchievementCategory category;
  final AchievementRarity rarity;
  final int pointsReward;
  final List<AchievementRequirement> requirements;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSecret;
  final Map<String, dynamic>? metadata;

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    this.iconUrl,
    required this.category,
    required this.rarity,
    required this.pointsReward,
    required this.requirements,
    required this.createdAt,
    required this.updatedAt,
    required this.isSecret,
    this.metadata,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) => _$AchievementFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementToJson(this);
}

@JsonSerializable()
class AchievementRequirement {
  final String id;
  final AchievementRequirementType type;
  final String target;
  final int requiredValue;
  final int currentValue;
  final bool isCompleted;

  const AchievementRequirement({
    required this.id,
    required this.type,
    required this.target,
    required this.requiredValue,
    required this.currentValue,
    required this.isCompleted,
  });

  factory AchievementRequirement.fromJson(Map<String, dynamic> json) => _$AchievementRequirementFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementRequirementToJson(this);

  double get progressPercentage => (currentValue / requiredValue).clamp(0.0, 1.0);
}

@JsonSerializable()
class UserAchievement {
  final String id;
  final String userId;
  final String achievementId;
  final Achievement achievement;
  final DateTime unlockedAt;
  final bool isNotified;

  const UserAchievement({
    required this.id,
    required this.userId,
    required this.achievementId,
    required this.achievement,
    required this.unlockedAt,
    required this.isNotified,
  });

  factory UserAchievement.fromJson(Map<String, dynamic> json) => _$UserAchievementFromJson(json);
  Map<String, dynamic> toJson() => _$UserAchievementToJson(this);
}

@JsonEnum()
enum AchievementCategory {
  @JsonValue('quests')
  quests,
  @JsonValue('social')
  social,
  @JsonValue('exploration')
  exploration,
  @JsonValue('collection')
  collection,
  @JsonValue('competition')
  competition,
  @JsonValue('milestone')
  milestone,
  @JsonValue('special')
  special,
}

@JsonEnum()
enum AchievementRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

@JsonEnum()
enum AchievementRequirementType {
  @JsonValue('quest_completion')
  questCompletion,
  @JsonValue('points_earned')
  pointsEarned,
  @JsonValue('level_reached')
  levelReached,
  @JsonValue('days_active')
  daysActive,
  @JsonValue('social_connections')
  socialConnections,
  @JsonValue('marketplace_transactions')
  marketplaceTransactions,
  @JsonValue('items_collected')
  itemsCollected,
}

extension AchievementCategoryExtension on AchievementCategory {
  String get displayName {
    switch (this) {
      case AchievementCategory.quests:
        return 'Quests';
      case AchievementCategory.social:
        return 'Social';
      case AchievementCategory.exploration:
        return 'Exploration';
      case AchievementCategory.collection:
        return 'Collection';
      case AchievementCategory.competition:
        return 'Competition';
      case AchievementCategory.milestone:
        return 'Milestone';
      case AchievementCategory.special:
        return 'Special';
    }
  }
}

extension AchievementRarityExtension on AchievementRarity {
  String get displayName {
    switch (this) {
      case AchievementRarity.common:
        return 'Common';
      case AchievementRarity.uncommon:
        return 'Uncommon';
      case AchievementRarity.rare:
        return 'Rare';
      case AchievementRarity.epic:
        return 'Epic';
      case AchievementRarity.legendary:
        return 'Legendary';
    }
  }

  int get pointsMultiplier {
    switch (this) {
      case AchievementRarity.common:
        return 1;
      case AchievementRarity.uncommon:
        return 2;
      case AchievementRarity.rare:
        return 3;
      case AchievementRarity.epic:
        return 5;
      case AchievementRarity.legendary:
        return 10;
    }
  }
}
