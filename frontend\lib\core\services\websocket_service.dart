import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

import '../constants/app_constants.dart';
import 'storage_service.dart';

class WebSocketService {
  static WebSocketService? _instance;
  static WebSocketService get instance => _instance ??= WebSocketService._();

  WebSocketService._();

  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;

  bool _isConnected = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const Duration _heartbeatInterval = Duration(seconds: 30);

  // Stream controllers for different types of messages
  final StreamController<Map<String, dynamic>> _notificationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _questUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _leaderboardController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _achievementController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _connectionController =
      StreamController<bool>.broadcast();

  // Getters for streams
  Stream<Map<String, dynamic>> get notificationStream => _notificationController.stream;
  Stream<Map<String, dynamic>> get questUpdateStream => _questUpdateController.stream;
  Stream<Map<String, dynamic>> get leaderboardStream => _leaderboardController.stream;
  Stream<Map<String, dynamic>> get achievementStream => _achievementController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;

  bool get isConnected => _isConnected;

  /// Connect to WebSocket
  Future<void> connect() async {
    if (_isConnected || _channel != null) {
      return;
    }

    try {
      final token = await StorageService.getAccessToken();
      if (token == null) {
        debugPrint('WebSocket: No access token found');
        return;
      }

      final uri = Uri.parse('${AppConstants.wsUrl}?token=$token');
      debugPrint('WebSocket: Connecting to $uri');

      _channel = WebSocketChannel.connect(uri);

      // Listen to connection state
      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _isConnected = true;
      _reconnectAttempts = 0;
      _connectionController.add(true);
      _startHeartbeat();

      debugPrint('WebSocket: Connected successfully');
    } catch (e) {
      debugPrint('WebSocket: Connection failed: $e');
      _onError(e);
    }
  }

  /// Disconnect from WebSocket
  Future<void> disconnect() async {
    _shouldReconnect = false;
    _stopHeartbeat();
    _stopReconnectTimer();

    if (_channel != null) {
      await _channel!.sink.close(status.goingAway);
      _channel = null;
    }

    _subscription?.cancel();
    _subscription = null;

    _isConnected = false;
    _connectionController.add(false);

    debugPrint('WebSocket: Disconnected');
  }

  /// Send message to WebSocket
  void sendMessage(Map<String, dynamic> message) {
    if (_isConnected && _channel != null) {
      try {
        final jsonMessage = jsonEncode(message);
        _channel!.sink.add(jsonMessage);
        debugPrint('WebSocket: Sent message: $jsonMessage');
      } catch (e) {
        debugPrint('WebSocket: Failed to send message: $e');
      }
    } else {
      debugPrint('WebSocket: Cannot send message - not connected');
    }
  }

  /// Send ping message
  void sendPing() {
    sendMessage({
      'type': 'ping',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Subscribe to specific event type
  void subscribe(String eventType) {
    sendMessage({
      'type': 'subscribe',
      'event_type': eventType,
    });
  }

  /// Unsubscribe from specific event type
  void unsubscribe(String eventType) {
    sendMessage({
      'type': 'unsubscribe',
      'event_type': eventType,
    });
  }

  /// Handle incoming messages
  void _onMessage(dynamic data) {
    try {
      final Map<String, dynamic> message = jsonDecode(data.toString());
      debugPrint('WebSocket: Received message: $message');

      final String? type = message['type'];
      if (type == null) return;

      switch (type) {
        case 'notification':
          _notificationController.add(message);
          break;
        case 'quest_update':
          _questUpdateController.add(message);
          break;
        case 'leaderboard_update':
          _leaderboardController.add(message);
          break;
        case 'achievement_unlocked':
          _achievementController.add(message);
          break;
        case 'pong':
          // Handle pong response
          debugPrint('WebSocket: Pong received');
          break;
        default:
          debugPrint('WebSocket: Unknown message type: $type');
      }
    } catch (e) {
      debugPrint('WebSocket: Failed to parse message: $e');
    }
  }

  /// Handle WebSocket errors
  void _onError(dynamic error) {
    debugPrint('WebSocket: Error occurred: $error');
    _isConnected = false;
    _connectionController.add(false);

    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }

  /// Handle WebSocket disconnection
  void _onDisconnected() {
    debugPrint('WebSocket: Connection closed');
    _isConnected = false;
    _connectionController.add(false);
    _stopHeartbeat();

    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('WebSocket: Max reconnection attempts reached');
      return;
    }

    _reconnectAttempts++;
    final delay = _reconnectDelay * _reconnectAttempts;

    debugPrint('WebSocket: Scheduling reconnect in ${delay.inSeconds} seconds (attempt $_reconnectAttempts)');

    _reconnectTimer = Timer(delay, () {
      if (_shouldReconnect) {
        connect();
      }
    });
  }

  /// Stop reconnection timer
  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Start heartbeat to keep connection alive
  void _startHeartbeat() {
    _stopHeartbeat();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (_isConnected) {
        sendPing();
      } else {
        _stopHeartbeat();
      }
    });
  }

  /// Stop heartbeat
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Dispose of all resources
  void dispose() {
    disconnect();
    _notificationController.close();
    _questUpdateController.close();
    _leaderboardController.close();
    _achievementController.close();
    _connectionController.close();
  }
}
