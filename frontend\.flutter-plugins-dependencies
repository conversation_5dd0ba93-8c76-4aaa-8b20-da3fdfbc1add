{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "device_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_image_compress_common", "path": "/root/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/root/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "/root/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/root/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/root/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/root/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/root/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "device_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_image_compress_common", "path": "/root/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/root/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/root/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_android", "path": "/root/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/root/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/root/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/root/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/root/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "device_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "/root/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_image_compress_macos", "path": "/root/.pub-cache/hosted/pub.dev/flutter_image_compress_macos-1.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "/root/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "/root/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/root/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/root/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/root/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/root/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "device_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_linux", "path": "/root/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "/root/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "/root/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/root/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/root/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/root/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "device_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_windows", "path": "/root/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "/root/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "/root/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/root/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/root/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/root/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "device_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_image_compress_web", "path": "/root/.pub-cache/hosted/pub.dev/flutter_image_compress_web-0.1.4+1/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "/root/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "/root/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.2/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/root/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/root/.pub-cache/hosted/pub.dev/shared_preferences_web-2.2.2/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/root/.pub-cache/hosted/pub.dev/url_launcher_web-2.2.3/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "device_info_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "flutter_image_compress", "dependencies": ["flutter_image_compress_common", "flutter_image_compress_web", "flutter_image_compress_macos", "flutter_image_compress_ohos"]}, {"name": "flutter_image_compress_common", "dependencies": []}, {"name": "flutter_image_compress_macos", "dependencies": []}, {"name": "flutter_image_compress_ohos", "dependencies": []}, {"name": "flutter_image_compress_web", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-06-04 15:56:56.451565", "version": "3.32.1", "swift_package_manager_enabled": {"ios": false, "macos": false}}