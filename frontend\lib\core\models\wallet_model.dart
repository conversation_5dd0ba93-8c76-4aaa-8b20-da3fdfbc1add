import 'package:json_annotation/json_annotation.dart';

part 'wallet_model.g.dart';

@JsonSerializable()
class Wallet {
  final String id;
  final String userId;
  final int balance;
  final List<WalletTransaction> transactions;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Wallet({
    required this.id,
    required this.userId,
    required this.balance,
    required this.transactions,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) => _$WalletFromJson(json);
  Map<String, dynamic> toJson() => _$WalletToJson(this);
}

@JsonSerializable()
class WalletTransaction {
  final String id;
  final String walletId;
  final WalletTransactionType type;
  final int amount;
  final String description;
  final String? referenceId;
  final WalletTransactionStatus status;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const WalletTransaction({
    required this.id,
    required this.walletId,
    required this.type,
    required this.amount,
    required this.description,
    this.referenceId,
    required this.status,
    required this.createdAt,
    this.metadata,
  });

  factory WalletTransaction.fromJson(Map<String, dynamic> json) => _$WalletTransactionFromJson(json);
  Map<String, dynamic> toJson() => _$WalletTransactionToJson(this);
}

@JsonEnum()
enum WalletTransactionType {
  @JsonValue('credit')
  credit,
  @JsonValue('debit')
  debit,
  @JsonValue('reward')
  reward,
  @JsonValue('purchase')
  purchase,
  @JsonValue('transfer')
  transfer,
  @JsonValue('refund')
  refund,
}

@JsonEnum()
enum WalletTransactionStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('cancelled')
  cancelled,
}

extension WalletTransactionTypeExtension on WalletTransactionType {
  String get displayName {
    switch (this) {
      case WalletTransactionType.credit:
        return 'Credit';
      case WalletTransactionType.debit:
        return 'Debit';
      case WalletTransactionType.reward:
        return 'Reward';
      case WalletTransactionType.purchase:
        return 'Purchase';
      case WalletTransactionType.transfer:
        return 'Transfer';
      case WalletTransactionType.refund:
        return 'Refund';
    }
  }

  bool get isPositive => this == WalletTransactionType.credit || 
                         this == WalletTransactionType.reward || 
                         this == WalletTransactionType.refund;
}
