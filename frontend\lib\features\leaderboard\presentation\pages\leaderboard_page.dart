import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LeaderboardPage extends ConsumerWidget {
  const LeaderboardPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    _buildTopThreeUsers(context),
                    _buildTabBar(context),
                  ],
                ),
              ),
            ];
          },
          body: TabBarView(
            children: [
              _buildLeaderboardList(context, 'Global'),
              _buildLeaderboardList(context, 'Weekly'),
              _buildLeaderboardList(context, 'Friends'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopThreeUsers(BuildContext context) {
    final topUsers = _getMockTopUsers();
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            'Top Questers',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _buildPodiumUser(context, topUsers[1], 2, 80),
              _buildPodiumUser(context, topUsers[0], 1, 100),
              _buildPodiumUser(context, topUsers[2], 3, 80),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPodiumUser(BuildContext context, Map<String, dynamic> user, int position, double height) {
    final colors = [
      Colors.amber, // Gold
      Colors.grey[400]!, // Silver
      Colors.brown[300]!, // Bronze
    ];
    
    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            CircleAvatar(
              radius: 30,
              backgroundImage: NetworkImage(user['avatar'] as String),
            ),
            if (position == 1)
              Positioned(
                top: -5,
                child: Icon(
                  Icons.emoji_events,
                  color: colors[position - 1],
                  size: 24,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          user['name'] as String,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          '${user['score']} pts',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: colors[position - 1],
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: height,
          width: 60,
          decoration: BoxDecoration(
            color: colors[position - 1],
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Center(
            child: Text(
              '$position',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: TabBar(
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        labelColor: Theme.of(context).colorScheme.onPrimary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurface,
        labelStyle: const TextStyle(fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'Global'),
          Tab(text: 'Weekly'),
          Tab(text: 'Friends'),
        ],
      ),
    );
  }

  Widget _buildLeaderboardList(BuildContext context, String type) {
    final users = _getMockLeaderboardUsers(type);
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        final position = index + 4; // Start from 4 since top 3 are shown above
        
        return _buildLeaderboardItem(context, user, position);
      },
    );
  }

  Widget _buildLeaderboardItem(BuildContext context, Map<String, dynamic> user, int position) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getPositionColor(context, position).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                '$position',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getPositionColor(context, position),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          CircleAvatar(
            radius: 20,
            backgroundImage: NetworkImage(user['avatar'] as String),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user['name'] as String,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Icon(
                      Icons.flag,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${user['questsCompleted']} quests',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Icon(
                      Icons.emoji_events,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${user['achievements']} achievements',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${user['score']}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              Text(
                'points',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => _showUserProfile(context, user),
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
    );
  }
  Color _getPositionColor(BuildContext context, int position) {
    if (position <= 10) {
      return Theme.of(context).colorScheme.primary;
    } else if (position <= 50) {
      return Colors.orange;
    } else {
      return Colors.grey;
    }
  }

  void _showUserProfile(BuildContext context, Map<String, dynamic> user) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundImage: NetworkImage(user['avatar'] as String),
            ),
            const SizedBox(height: 16),
            Text(
              user['name'] as String,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${user['score']} points',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildUserStat(context, 'Quests', '${user['questsCompleted']}'),
                _buildUserStat(context, 'Achievements', '${user['achievements']}'),
                _buildUserStat(context, 'Streak', '${user['streak'] ?? 0} days'),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _sendFriendRequest(context, user),
                    child: const Text('Add Friend'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _viewFullProfile(context, user),
                    child: const Text('View Profile'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserStat(BuildContext context, String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  void _sendFriendRequest(BuildContext context, Map<String, dynamic> user) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Friend request sent to ${user['name']}')),
    );
  }

  void _viewFullProfile(BuildContext context, Map<String, dynamic> user) {
    Navigator.of(context).pop();
    // Navigate to full profile page
  }

  List<Map<String, dynamic>> _getMockTopUsers() {
    return [
      {
        'name': 'Sarah Chen',
        'avatar': 'https://i.pravatar.cc/150?img=1',
        'score': 2450,
        'questsCompleted': 42,
        'achievements': 15,
        'streak': 12,
      },
      {
        'name': 'Alex Johnson',
        'avatar': 'https://i.pravatar.cc/150?img=2',
        'score': 2380,
        'questsCompleted': 38,
        'achievements': 14,
        'streak': 8,
      },
      {
        'name': 'Maria Garcia',
        'avatar': 'https://i.pravatar.cc/150?img=3',
        'score': 2290,
        'questsCompleted': 35,
        'achievements': 12,
        'streak': 15,
      },
    ];
  }

  List<Map<String, dynamic>> _getMockLeaderboardUsers(String type) {
    final baseUsers = [
      {
        'name': 'David Wilson',
        'avatar': 'https://i.pravatar.cc/150?img=4',
        'score': 2180,
        'questsCompleted': 32,
        'achievements': 11,
        'streak': 6,
      },
      {
        'name': 'Emma Davis',
        'avatar': 'https://i.pravatar.cc/150?img=5',
        'score': 2120,
        'questsCompleted': 29,
        'achievements': 10,
        'streak': 9,
      },
      {
        'name': 'James Brown',
        'avatar': 'https://i.pravatar.cc/150?img=6',
        'score': 2050,
        'questsCompleted': 28,
        'achievements': 9,
        'streak': 4,
      },
      {
        'name': 'Lisa Miller',
        'avatar': 'https://i.pravatar.cc/150?img=7',
        'score': 1980,
        'questsCompleted': 26,
        'achievements': 8,
        'streak': 7,
      },
      {
        'name': 'Michael Taylor',
        'avatar': 'https://i.pravatar.cc/150?img=8',
        'score': 1890,
        'questsCompleted': 24,
        'achievements': 7,
        'streak': 3,
      },
    ];

    // You could modify data based on type (Global, Weekly, Friends)
    switch (type) {
      case 'Weekly':
        return baseUsers.map((user) => {
          ...user,
          'score': (user['score'] as int) ~/ 4, // Weekly scores are lower
        }).toList();
      case 'Friends':
        return baseUsers.take(3).toList(); // Fewer friends
      default:
        return baseUsers;
    }
  }
}
