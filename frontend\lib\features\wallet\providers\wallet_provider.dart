import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/wallet_model.dart';
// import '../../../core/services/api_service.dart';

// Wallet state provider
final walletProvider = FutureProvider<Wallet>((ref) async {
  // final apiService = ref.read(apiServiceProvider);
  // For now, return mock data until API is implemented
  return _getMockWallet();
});

// Wallet balance provider (derived from wallet)
final walletBalanceProvider = Provider<int>((ref) {
  final walletAsync = ref.watch(walletProvider);
  return walletAsync.when(
    data: (wallet) => wallet.balance,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Recent transactions provider
final recentTransactionsProvider = Provider<List<WalletTransaction>>((ref) {
  final walletAsync = ref.watch(walletProvider);
  return walletAsync.when(
    data: (wallet) => wallet.transactions.take(5).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Send tokens provider
final sendTokensProvider = StateNotifierProvider<SendTokensNotifier, SendTokensState>((ref) {
  return SendTokensNotifier(ref);
});

class SendTokensNotifier extends StateNotifier<SendTokensState> {
  final Ref _ref;

  SendTokensNotifier(this._ref) : super(const SendTokensState());

  Future<void> sendTokens({
    required String recipientId,
    required int amount,
    required String description,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
      try {
      // final apiService = _ref.read(apiServiceProvider);
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2)); // Mock delay
      
      // Mock success
      state = state.copyWith(
        isLoading: false,
        success: true,
      );
      
      // Refresh wallet data
      _ref.invalidate(walletProvider);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void resetState() {
    state = const SendTokensState();
  }
}

class SendTokensState {
  final bool isLoading;
  final String? error;
  final bool success;

  const SendTokensState({
    this.isLoading = false,
    this.error,
    this.success = false,
  });

  SendTokensState copyWith({
    bool? isLoading,
    String? error,
    bool? success,
  }) {
    return SendTokensState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      success: success ?? this.success,
    );
  }
}

// Transaction history provider
final transactionHistoryProvider = FutureProvider.family<List<WalletTransaction>, int>((ref, page) async {
  // final apiService = ref.read(apiServiceProvider);
  // TODO: Implement actual API call with pagination
  return _getMockTransactions();
});

// Helper function to create mock wallet data
Wallet _getMockWallet() {
  return Wallet(
    id: 'wallet_1',
    userId: 'user_1',
    balance: 1250,
    transactions: _getMockTransactions(),
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    updatedAt: DateTime.now(),
  );
}

List<WalletTransaction> _getMockTransactions() {
  return [
    WalletTransaction(
      id: 'tx_1',
      walletId: 'wallet_1',
      type: WalletTransactionType.reward,
      amount: 50,
      description: 'Quest completion: Morning Meditation',
      status: WalletTransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    WalletTransaction(
      id: 'tx_2',
      walletId: 'wallet_1',
      type: WalletTransactionType.purchase,
      amount: 25,
      description: 'Marketplace purchase: Avatar Customization',
      status: WalletTransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(hours: 6)),
    ),
    WalletTransaction(
      id: 'tx_3',
      walletId: 'wallet_1',
      type: WalletTransactionType.reward,
      amount: 75,
      description: 'Quest completion: Photo Challenge',
      status: WalletTransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    WalletTransaction(
      id: 'tx_4',
      walletId: 'wallet_1',
      type: WalletTransactionType.credit,
      amount: 100,
      description: 'Welcome bonus',
      status: WalletTransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    WalletTransaction(
      id: 'tx_5',
      walletId: 'wallet_1',
      type: WalletTransactionType.reward,
      amount: 30,
      description: 'Daily streak bonus',
      status: WalletTransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    WalletTransaction(
      id: 'tx_6',
      walletId: 'wallet_1',
      type: WalletTransactionType.transfer,
      amount: 20,
      description: 'Sent to friend',
      status: WalletTransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(days: 4)),
    ),
    WalletTransaction(
      id: 'tx_7',
      walletId: 'wallet_1',
      type: WalletTransactionType.reward,
      amount: 40,
      description: 'Achievement unlocked: First Week',
      status: WalletTransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
  ];
}
