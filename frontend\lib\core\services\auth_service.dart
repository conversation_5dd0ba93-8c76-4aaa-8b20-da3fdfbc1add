import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_model.dart';
import 'storage_service.dart';

class AuthService {
  
  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Get stored access token
  Future<String?> getToken() async {
    return await StorageService.getAccessToken();
  }

  // Get stored refresh token
  Future<String?> getRefreshToken() async {
    return await StorageService.getRefreshToken();
  }

  // Store authentication tokens
  Future<void> storeTokens({
    required String accessToken,
    String? refreshToken,
  }) async {
    await StorageService.saveAccessToken(accessToken);
    if (refreshToken != null) {
      await StorageService.saveRefreshToken(refreshToken);
    }
  }

  // Store user data
  Future<void> storeUser(User user) async {
    await StorageService.saveUser(user.toJson());
  }

  // Get stored user data
  Future<User?> getStoredUser() async {
    final userData = StorageService.getUser();
    if (userData != null) {
      try {
        return User.fromJson(userData);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Login with email and password
  Future<AuthResult> login({
    required String email,
    required String password,
  }) async {
    try {
      // This would typically make an API call to the backend
      // For now, we'll simulate a successful login
      
      // Simulate API delay
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo purposes, accept any email/password combination
      if (email.isNotEmpty && password.isNotEmpty) {
        // Store dummy tokens
        await storeTokens(
          accessToken: 'dummy_access_token_${DateTime.now().millisecondsSinceEpoch}',
          refreshToken: 'dummy_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        );
        
        // Create dummy user
        final dummyUser = User(
          id: 'user_123',
          email: email,
          username: email.split('@').first,
          firstName: 'John',
          lastName: 'Doe',
          role: UserRole.user,
          status: UserStatus.active,
          level: 1,
          totalPoints: 0,
          totalQuestCompleted: 0,
          totalAchievements: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          permissions: ['user:read', 'quest:join'],
        );
        
        await storeUser(dummyUser);
        
        return AuthResult.success(user: dummyUser);
      } else {
        return AuthResult.failure(message: 'Invalid email or password');
      }
    } catch (e) {
      return AuthResult.failure(message: 'Login failed: ${e.toString()}');
    }
  }

  // Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String username,
  }) async {
    try {
      // Simulate API delay
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo purposes, accept any valid input
      if (email.isNotEmpty && password.isNotEmpty && username.isNotEmpty) {
        // Store dummy tokens
        await storeTokens(
          accessToken: 'dummy_access_token_${DateTime.now().millisecondsSinceEpoch}',
          refreshToken: 'dummy_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        );
        
        // Create new user
        final newUser = User(
          id: 'user_${DateTime.now().millisecondsSinceEpoch}',
          email: email,
          username: username,
          firstName: firstName,
          lastName: lastName,
          role: UserRole.user,
          status: UserStatus.active,
          level: 1,
          totalPoints: 0,
          totalQuestCompleted: 0,
          totalAchievements: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          permissions: ['user:read', 'quest:join'],
        );
        
        await storeUser(newUser);
        
        return AuthResult.success(user: newUser);
      } else {
        return AuthResult.failure(message: 'All fields are required');
      }
    } catch (e) {
      return AuthResult.failure(message: 'Registration failed: ${e.toString()}');
    }
  }
  // Logout user
  Future<void> logout() async {
    await StorageService.clearTokens();
    await StorageService.clearUser();
  }

  // Refresh access token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        return false;
      }

      // This would typically make an API call to refresh the token
      // For now, we'll simulate a successful refresh
      await Future.delayed(const Duration(seconds: 1));
      
      await storeTokens(
        accessToken: 'refreshed_access_token_${DateTime.now().millisecondsSinceEpoch}',
        refreshToken: refreshToken,
      );
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Reset password
  Future<bool> resetPassword({required String email}) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo purposes, always return success
      return true;
    } catch (e) {
      return false;
    }
  }
}

class AuthResult {
  final bool isSuccess;
  final String? message;
  final User? user;

  AuthResult._({
    required this.isSuccess,
    this.message,
    this.user,
  });

  factory AuthResult.success({User? user}) {
    return AuthResult._(
      isSuccess: true,
      user: user,
    );
  }

  factory AuthResult.failure({required String message}) {
    return AuthResult._(
      isSuccess: false,
      message: message,
    );
  }
}

final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});
