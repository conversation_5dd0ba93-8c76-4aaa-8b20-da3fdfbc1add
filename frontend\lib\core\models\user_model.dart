import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User {
  final String id;
  final String email;
  final String username;
  final String firstName;
  final String lastName;
  final String? avatar;
  final UserRole role;
  final UserStatus status;
  final int level;
  final int totalPoints;
  final int totalQuestCompleted;
  final int totalAchievements;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? bio;
  final List<String> permissions;
  
  const User({
    required this.id,
    required this.email,
    required this.username,
    required this.firstName,
    required this.lastName,
    this.avatar,
    required this.role,
    required this.status,
    required this.level,
    required this.totalPoints,
    required this.totalQuestCompleted,
    required this.totalAchievements,
    required this.createdAt,
    required this.updatedAt,
    this.bio,
    required this.permissions,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    String? id,
    String? email,
    String? username,
    String? firstName,
    String? lastName,
    String? avatar,
    UserRole? role,
    UserStatus? status,
    int? level,
    int? totalPoints,
    int? totalQuestCompleted,
    int? totalAchievements,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? bio,
    List<String>? permissions,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      status: status ?? this.status,
      level: level ?? this.level,
      totalPoints: totalPoints ?? this.totalPoints,
      totalQuestCompleted: totalQuestCompleted ?? this.totalQuestCompleted,
      totalAchievements: totalAchievements ?? this.totalAchievements,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      bio: bio ?? this.bio,
      permissions: permissions ?? this.permissions,
    );
  }
}

@JsonEnum()
enum UserRole {
  @JsonValue('user')
  user,
  @JsonValue('admin')
  admin,
  @JsonValue('moderator')
  moderator,
  @JsonValue('developer')
  developer,
}

@JsonEnum()
enum UserStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
  @JsonValue('banned')
  banned,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.user:
        return 'User';
      case UserRole.admin:
        return 'Admin';
      case UserRole.moderator:
        return 'Moderator';
      case UserRole.developer:
        return 'Developer';
    }
  }

  bool get isAdmin => this == UserRole.admin || this == UserRole.developer;
  bool get isModerator => this == UserRole.moderator || isAdmin;
}

extension UserStatusExtension on UserStatus {
  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'Active';
      case UserStatus.inactive:
        return 'Inactive';
      case UserStatus.suspended:
        return 'Suspended';
      case UserStatus.banned:
        return 'Banned';
    }
  }

  bool get isActive => this == UserStatus.active;
}
