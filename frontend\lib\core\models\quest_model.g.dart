// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quest_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Quest _$QuestFromJson(Map<String, dynamic> json) => Quest(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String?,
      category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
      difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
      status: $enumDecode(_$Quest<PERSON>tatusEnumMap, json['status']),
      pointsReward: (json['pointsReward'] as num).toInt(),
      experienceReward: (json['experienceReward'] as num).toInt(),
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      maxParticipants: (json['maxParticipants'] as num).toInt(),
      currentParticipants: (json['currentParticipants'] as num).toInt(),
      steps: (json['steps'] as List<dynamic>)
          .map((e) => QuestStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      prerequisites: (json['prerequisites'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      rewards: (json['rewards'] as List<dynamic>)
          .map((e) => QuestReward.fromJson(e as Map<String, dynamic>))
          .toList(),
      isPublic: json['isPublic'] as bool,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$QuestToJson(Quest instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'category': _$QuestCategoryEnumMap[instance.category]!,
      'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
      'status': _$QuestStatusEnumMap[instance.status]!,
      'pointsReward': instance.pointsReward,
      'experienceReward': instance.experienceReward,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'maxParticipants': instance.maxParticipants,
      'currentParticipants': instance.currentParticipants,
      'steps': instance.steps,
      'prerequisites': instance.prerequisites,
      'rewards': instance.rewards,
      'isPublic': instance.isPublic,
      'metadata': instance.metadata,
    };

const _$QuestCategoryEnumMap = {
  QuestCategory.daily: 'daily',
  QuestCategory.weekly: 'weekly',
  QuestCategory.monthly: 'monthly',
  QuestCategory.special: 'special',
  QuestCategory.community: 'community',
  QuestCategory.personal: 'personal',
  QuestCategory.education: 'education',
  QuestCategory.fitness: 'fitness',
  QuestCategory.creative: 'creative',
  QuestCategory.social: 'social',
};

const _$QuestDifficultyEnumMap = {
  QuestDifficulty.easy: 'easy',
  QuestDifficulty.medium: 'medium',
  QuestDifficulty.hard: 'hard',
  QuestDifficulty.expert: 'expert',
};

const _$QuestStatusEnumMap = {
  QuestStatus.draft: 'draft',
  QuestStatus.published: 'published',
  QuestStatus.active: 'active',
  QuestStatus.completed: 'completed',
  QuestStatus.expired: 'expired',
  QuestStatus.cancelled: 'cancelled',
};

QuestStep _$QuestStepFromJson(Map<String, dynamic> json) => QuestStep(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      order: (json['order'] as num).toInt(),
      type: $enumDecode(_$QuestStepTypeEnumMap, json['type']),
      requirements: json['requirements'] as Map<String, dynamic>?,
      isCompleted: json['isCompleted'] as bool,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$QuestStepToJson(QuestStep instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'order': instance.order,
      'type': _$QuestStepTypeEnumMap[instance.type]!,
      'requirements': instance.requirements,
      'isCompleted': instance.isCompleted,
      'completedAt': instance.completedAt?.toIso8601String(),
    };

const _$QuestStepTypeEnumMap = {
  QuestStepType.action: 'action',
  QuestStepType.submission: 'submission',
  QuestStepType.verification: 'verification',
  QuestStepType.timer: 'timer',
  QuestStepType.location: 'location',
  QuestStepType.social: 'social',
};

QuestReward _$QuestRewardFromJson(Map<String, dynamic> json) => QuestReward(
      id: json['id'] as String,
      type: $enumDecode(_$QuestRewardTypeEnumMap, json['type']),
      name: json['name'] as String,
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$QuestRewardToJson(QuestReward instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$QuestRewardTypeEnumMap[instance.type]!,
      'name': instance.name,
      'description': instance.description,
      'quantity': instance.quantity,
      'imageUrl': instance.imageUrl,
      'metadata': instance.metadata,
    };

const _$QuestRewardTypeEnumMap = {
  QuestRewardType.points: 'points',
  QuestRewardType.badge: 'badge',
  QuestRewardType.item: 'item',
  QuestRewardType.currency: 'currency',
  QuestRewardType.experience: 'experience',
  QuestRewardType.nft: 'nft',
};
