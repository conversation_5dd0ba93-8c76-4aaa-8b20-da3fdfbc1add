x-common-variables: &common-variables
  APP_ENV: ${APP_ENV:-development}
  APP_NAME: ${APP_NAME:-quester}
  DATABASE_USERNAME: ${DATABASE_USERNAME:-keshabalive}
  DATABASE_PASSWORD: ${DATABASE_PASSWORD:-9871}
  DATABASE_DATABASE: ${DATABASE_DATABASE:-quester}

services:
  frontend:
    container_name: quester-frontend
    build:
      context: .
      dockerfile: settings/docker/dockerfile.frontend
      target: ${APP_ENV:-development}
      args:
        FLUTTER_ENV: ${APP_ENV:-development}
        FLUTTER_WEB_PORT: ${FRONTEND_PORT:-3000}
        BUILD_ENV: ${APP_ENV:-development}
    ports:
      - "${FRONTEND_PORT:-3000}:${FRONTEND_PORT:-3000}"
    volumes:
      - ./frontend:/app:delegated
      - ./settings/nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      - frontend_cache:/app/.dart_tool:cached
      - flutter_pub_cache:/root/.pub-cache:cached
    environment:
      FLUTTER_ENV: ${APP_ENV:-development}
      FLUTTER_WEB_PORT: ${FRONTEND_PORT:-3000}
      API_URL: http://backend:${BACKEND_PORT:-8080}/api
      WS_URL: ws://backend:${BACKEND_PORT:-8080}/ws
      APP_NAME: ${APP_NAME:-quester}
      APP_VERSION: 1.0.0
      ENABLE_BLOCKCHAIN: ${BLOCKCHAIN_ENABLED:-false}
      ENABLE_AI_FEATURES: true
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - quester-network

  backend:
    container_name: quester-backend
    build:
      context: .
      dockerfile: settings/docker/dockerfile.backend
      target: ${APP_ENV:-development}
      args:
        GO_ENV: ${APP_ENV:-development}
        PORT: ${BACKEND_PORT:-8080}
    ports:
      - "${BACKEND_PORT:-8080}:${BACKEND_PORT:-8080}"
    volumes:
      - ./backend:/app:delegated
      - ./backend/config/docker.env:/app/.env:ro
      - backend_cache:/app/pkg/mod:cached
    environment:
      <<: *common-variables
      GO_ENV: ${APP_ENV:-development}
      BACKEND_HOST: ${BACKEND_HOST:-0.0.0.0}
      BACKEND_PORT: ${BACKEND_PORT:-8080}
      API_VERSION: ${API_VERSION:-1}
      SERVER_READ_TIMEOUT: ${SERVER_READ_TIMEOUT:-10s}
      SERVER_WRITE_TIMEOUT: ${SERVER_WRITE_TIMEOUT:-10s}
      SERVER_IDLE_TIMEOUT: ${SERVER_IDLE_TIMEOUT:-120s}
      SERVER_SHUTDOWN_TIMEOUT: ${SERVER_SHUTDOWN_TIMEOUT:-20s}
      SERVER_ADDRESS: ${SERVER_ADDRESS:-:8080}
      DATABASE_HOST: postgres
      DATABASE_PORT: ${DATABASE_PORT:-5432}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-keshabalive}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-9871}
      DATABASE_DATABASE: ${DATABASE_DATABASE:-quester}
      DATABASE_DRIVER: ${DATABASE_DRIVER:-postgres}
      DATABASE_MAX_IDLE_CONNS: ${DATABASE_MAX_IDLE_CONNS:-10}
      DATABASE_MAX_OPEN_CONNS: ${DATABASE_MAX_OPEN_CONNS:-100}
      DATABASE_MAX_LIFETIME: ${DATABASE_MAX_LIFETIME:-1h}
      DATABASE_LOG_LEVEL: ${DATABASE_LOG_LEVEL:-info}
      DATABASE_SSL_MODE: ${DATABASE_SSL_MODE:-disable}
      DATABASE_TIMEZONE: ${DATABASE_TIMEZONE:-UTC}
      REDIS_HOST: redis
      REDIS_PORT: ${REDIS_PORT:-6379}
      REDIS_ADDR: ${REDIS_ADDR:-localhost:6379}
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      REDIS_DB: ${REDIS_DB:-0}
      AUTH_JWT_SECRET: ${AUTH_JWT_SECRET:-your-secret-key-here}
      JWT_SECRET: ${JWT_SECRET:-your-secret-key-here}
      AUTH_JWT_EXPIRATION_HOURS: ${AUTH_JWT_EXPIRATION_HOURS:-24}
      CORS_ALLOW_ORIGINS: ${CORS_ALLOW_ORIGINS:-http://localhost:8080}
      CORS_ALLOW_METHODS: ${CORS_ALLOW_METHODS:-GET,POST,PUT,DELETE,OPTIONS}
      CORS_ALLOW_HEADERS: ${CORS_ALLOW_HEADERS:-Origin,Content-Type,Accept,Authorization}
      CORS_ALLOW_CREDENTIALS: ${CORS_ALLOW_CREDENTIALS:-true}
      CORS_EXPOSE_HEADERS: ${CORS_EXPOSE_HEADERS:-}
      CORS_MAX_AGE: ${CORS_MAX_AGE:-86400}
      AI_MODEL_ENDPOINT: ${AI_MODEL_ENDPOINT:-http://localhost:5000/predict}
      AI_API_KEY: ${AI_API_KEY:-your-api-key-here}
      BLOCKCHAIN_ENABLED: ${BLOCKCHAIN_ENABLED:-false}
      BLOCKCHAIN_NETWORK: ${BLOCKCHAIN_NETWORK:-testnet}
      BLOCKCHAIN_CHAIN_ID: ${BLOCKCHAIN_CHAIN_ID:-1}
      BLOCKCHAIN_RPC: ${BLOCKCHAIN_RPC:-https://rpc.example.com}
      BLOCKCHAIN_QUEST_TOKEN_ADDRESS: ${BLOCKCHAIN_QUEST_TOKEN_ADDRESS:-0x1234567890abcdef}
      BLOCKCHAIN_DIFFICULTY: ${BLOCKCHAIN_DIFFICULTY:-4}
      PORT: ${PORT:-8080}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - quester-network

  postgres:
    image: postgres:latest
    container_name: quester-postgres
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    environment:
      <<: *common-variables
      POSTGRES_USER: ${DATABASE_USERNAME:-keshabalive}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-9871}
      POSTGRES_DB: ${DATABASE_DATABASE:-quester}
    volumes:
      - postgres_data:/var/lib/postgresql/data:delegated
      - postgres_logs:/var/log/postgresql:delegated
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME:-keshabalive} -d ${DATABASE_DATABASE:-quester}"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    command: postgres -c logging_collector=off
    networks:
      - quester-network

  redis:
    container_name: quester-redis
    image: redis:latest
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data:delegated
      - redis_cache:/data/cache:delegated
      - ./settings/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - quester-network

  pgadmin:
    container_name: quester-pgadmin
    image: dpage/pgadmin4:latest
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - quester-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: quester-redis-commander
    ports:
      - "${REDIS_ADMIN_PORT:-8081}:8081"
    volumes:
      - redis_admin_data:/db:delegated
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - quester-network

networks:
  quester-network:
    driver: bridge
    name: quester-network

volumes:
  postgres_data:
    name: quester_postgres_data
  postgres_logs:
    name: quester_postgres_logs
  redis_data:
    name: quester_redis_data
  backend_cache:
    name: quester_backend_cache
  frontend_cache:
    name: quester_frontend_cache
  flutter_pub_cache:
    name: quester_flutter_pub_cache
  pgadmin_data:
    name: quester_pgadmin_data
  redis_admin_data:
    name: quester_redis_admin_data
  redis_cache:
    name: quester_redis_cache







